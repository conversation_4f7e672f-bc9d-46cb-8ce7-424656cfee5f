// shared/components/notifications/BetNotification.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { CashierTurboPlaceBetDetails, BetDetailsData } from '@/shared/types/user-management-types';
import { useBetDetailsLazyQuery } from '@/shared/query/useBetDetailsQuery';
import { PrimaryButton } from '@/shared/UI/components';
import { SVGLoader } from '@/shared/UI/components/icons';
import QRCode from 'qrcode';

interface BetNotificationProps {
  notification: CashierTurboPlaceBetDetails;
  onClose?: () => void;
  className?: string;
}

/**
 * Enhanced printer detection interface
 */
interface PrinterInfo {
  id: string;
  name: string;
  type: 'usb' | 'wifi' | 'network' | 'unknown';
  status: 'ready' | 'busy' | 'offline' | 'unknown';
}

/**
 * SimpleBetSuccessPopup Component
 *
 * A simplified success popup that displays when a bet is placed successfully.
 * Replaces the existing BetNotification with enhanced print functionality.
 */
export const BetNotification: React.FC<BetNotificationProps> = ({
  notification,
  onClose,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [_availablePrinters, setAvailablePrinters] = useState<PrinterInfo[]>([]);
  const [isPrintingDetectionLoading, setIsPrintingDetectionLoading] = useState(false);
  const [betDetailsData, setBetDetailsData] = useState<BetDetailsData | null>(null);
  const [isLoadingBetDetails, setIsLoadingBetDetails] = useState(false);

  // Hook for fetching detailed bet information
  const { fetchBetDetails } = useBetDetailsLazyQuery();

  const handleClose = useCallback(() => {
    setIsVisible(false);
    onClose?.();
  }, [onClose]);

  // Handle ESC key press for accessibility
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isVisible) {
        handleClose();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleEscapeKey);
      // Prevent body scroll when popup is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'unset';
    };
  }, [isVisible, handleClose]);

  // Fetch detailed bet information when popup opens
  useEffect(() => {
    if (isVisible && notification.transactionId && notification.provider && !betDetailsData && !isLoadingBetDetails) {
      setIsLoadingBetDetails(true);
      fetchBetDetails(notification.transactionId, notification.provider)
        .then((response) => {
          setBetDetailsData(response.data);
        })
        .catch((error) => {
          // Handle error silently or show user-friendly message
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.error('Failed to fetch bet details:', error);
          }
        })
        .finally(() => {
          setIsLoadingBetDetails(false);
        });
    }
  }, [isVisible, notification.transactionId, notification.provider, betDetailsData, isLoadingBetDetails, fetchBetDetails]);

  // Enhanced printer detection function
  const detectPrinters = useCallback(async (): Promise<PrinterInfo[]> => {
    setIsPrintingDetectionLoading(true);
    const detectedPrinters: PrinterInfo[] = [];

    try {
      // Modern Web API for printer detection (if available)
      if ('navigator' in window && 'mediaDevices' in navigator) {
        try {
          // Check for USB printers using WebUSB API (if available)
          if ('usb' in navigator) {
            const usbDevices = await (navigator as any).usb.getDevices();
            usbDevices.forEach((device: any, index: number) => {
              // Check if device is a printer (class code 7 for printers)
              if (device.deviceClass === 7 || device.productName?.toLowerCase().includes('printer')) {
                detectedPrinters.push({
                  id: `usb-${device.vendorId}-${device.productId}`,
                  name: device.productName || `USB Printer ${index + 1}`,
                  type: 'usb',
                  status: 'ready'
                });
              }
            });
          }
        } catch (error) {
          // USB printer detection not available - this is expected in most browsers
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.log('USB printer detection not available:', error);
          }
        }
      }

      // Check for network printers using fetch to common printer ports
      const commonPrinterIPs = ['*************', '*************', '**********'];
      const printerCheckPromises = commonPrinterIPs.map(async (ip, index) => {
        try {
          // Try to connect to common printer web interfaces
          const controller = new AbortController();
          setTimeout(() => controller.abort(), 1000); // 1 second timeout

          await fetch(`http://${ip}`, {
            method: 'HEAD',
            signal: controller.signal,
            mode: 'no-cors'
          });

          return {
            id: `network-${ip}`,
            name: `Network Printer ${index + 1} (${ip})`,
            type: 'network' as const,
            status: 'ready' as const
          };
        } catch {
          return null;
        }
      });

      const networkPrinters = (await Promise.all(printerCheckPromises)).filter(Boolean) as PrinterInfo[];
      detectedPrinters.push(...networkPrinters);

      // Fallback: Add default system printer
      if (detectedPrinters.length === 0) {
        detectedPrinters.push({
          id: 'system-default',
          name: 'Default System Printer',
          type: 'unknown',
          status: 'ready'
        });
      }

    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('Printer detection failed:', error);
      }
      // Add fallback printer
      detectedPrinters.push({
        id: 'fallback-printer',
        name: 'System Printer',
        type: 'unknown',
        status: 'unknown'
      });
    } finally {
      setIsPrintingDetectionLoading(false);
    }

    return detectedPrinters;
  }, []);

  // Enhanced print function with comprehensive bet details
  const handlePrintBetslip = useCallback(async () => {
    if (!betDetailsData) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.warn('No bet details available for printing');
      }
      return;
    }

    // Detect available printers
    const printers = await detectPrinters();
    setAvailablePrinters(printers);

    // Generate QR code for bet verification
    let qrCodeDataUrl = '';
    try {
      // Create a verification URL using bet ID and transaction ID
      const verificationUrl = `${window.location.origin}/bet-verification?betId=${betDetailsData.betDetails?.betId}&transactionId=${notification.transactionId}`;
      qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
        width: 100,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('Failed to generate QR code:', error);
      }
    }

    // Create comprehensive print content using API response structure
    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Bet Slip - ${betDetailsData.betDetails?.betId || 'N/A'}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 10px;
              background-color: white;
              color: black;
              line-height: 1.6;
              font-size: 10px;
            }
            .header {
              text-align: center;
              border-bottom: 2px solid #000;
              margin-bottom: 5px;
            }
            .title {
              color: black;
              font-size: 14px;
              font-weight: bold;
              margin: 0;
            }
            .subtitle {
              color: black;
              font-size: 12px;
              margin: 10px 0 0 0;
              font-weight: normal;
            }
            .section {
              margin: 10px 0;
              background-color: white;
            }
            .section-title {
              font-size: 14px;
              font-weight: bold;
              color: black;
              margin-bottom: 5px;
              border-bottom: 2px solid #000;
              text-transform: uppercase;
            }
            .detail-row {
              display: flex;
              justify-content: space-between;
              padding: 8px 0;
              border-bottom: 1px solid #ccc;
            }
            .detail-row:last-child {
              border-bottom: none;
            }
            .label {
              font-weight: bold;
              color: black;
              flex: 1;
              font-size: 10px;
            }
            .value {
              color: black;
              flex: 2;
              text-align: right;
              font-size: 10px;
            }
            .bet-list {
              margin-top: 5px;
            }
            .bet-item {
              background: white;
              padding: 5px;
              margin: 5px 0;
            }
            .qr-section {
              margin-top: 10px;
              text-align: center;
              padding:5px;
              border-top: 2px solid #000;
            }
            .qr-code {
              width: 100px;
              height: 100px;
              margin: 0 auto 10px;
              border: 2px solid #000;
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: white;
              font-size: 14px;
              color: black;
            }
            .qr-label {
              font-size: 12px;
              color: black;
              font-weight: bold;
              margin-top: 5px;
            }
            .footer {
              margin-top: 5px;
              text-align: center;
              font-size: 10px;
              color: black;
              border-top: 2px solid #000;
              padding-top: 5px;
            }
            @media print {
              body {
                margin: 5px;
                background-color: white !important;
                color: black !important;
              }
              .section {
                break-inside: avoid;
                page-break-inside: avoid;
              }
              .qr-section {
                break-inside: avoid;
                page-break-inside: avoid;
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1 class="title">BET SLIP</h1>
            <p class="subtitle">Transaction Confirmation</p>
          </div>

          <!-- Market Details Section -->
          <div class="section">
            <div class="section-title">Market Information</div>
            <div class="detail-row">
              <span class="label">Market ID:</span>
              <span class="value">${betDetailsData.marketDetail?.marketId || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Market Name:</span>
              <span class="value">${betDetailsData.marketDetail?.marketName || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Market Status:</span>
              <span class="value">${betDetailsData.marketDetail?.marketStatus || 'N/A'}</span>
            </div>
          </div>

          <!-- Bet Details Section -->
          <div class="section">
            <div class="section-title">Bet Details</div>
            <div class="detail-row">
              <span class="label">Bet ID:</span>
              <span class="value">${betDetailsData.betDetails?.betId || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Bet Type:</span>
              <span class="value">${betDetailsData.betDetails?.betType || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Settlement Status:</span>
              <span class="value">${betDetailsData.betDetails?.settlementStatus || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Bet Amount:</span>
              <span class="value">$${(betDetailsData.betDetails?.betAmount || 0).toFixed(2)}</span>
            </div>
            <div class="detail-row">
              <span class="label">Settlement Amount:</span>
              <span class="value">$${(betDetailsData.betDetails?.settlementAmount || 0).toFixed(2)}</span>
            </div>
            <div class="detail-row">
              <span class="label">Created Date:</span>
              <span class="value">${betDetailsData.betDetails?.createdDate ? new Date(betDetailsData.betDetails.createdDate).toLocaleString() : 'N/A'}</span>
            </div>
          </div>

          <!-- Individual Bets Section -->
          ${betDetailsData.betList && betDetailsData.betList.length > 0 ? `
          <div class="section">
            <div class="section-title">Individual Bets</div>
            <div class="bet-list">
              ${betDetailsData.betList.map((bet, index) => `
                <div class="bet-item">
                  <div class="detail-row">
                    <span class="label">Bet ${index + 1} ID:</span>
                    <span class="value">${bet.betId || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Market Name:</span>
                    <span class="value">${bet.marketName || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Bet Type:</span>
                    <span class="value">${bet.betType || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Rate:</span>
                    <span class="value">${bet.rate || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Stake:</span>
                    <span class="value">$${(bet.stake || 0).toFixed(2)}</span>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          ` : ''}

          <!-- QR Code Section -->
          ${qrCodeDataUrl ? `
          <div class="qr-section">
            <img src="${qrCodeDataUrl}" alt="Bet Verification QR Code" class="qr-code" style="width: 100px; height: 100px; margin: 0 auto; display: block;" />
            <div class="qr-label">Scan for bet verification</div>
          </div>
          ` : `
          <div class="qr-section">
            <div class="qr-code">QR CODE</div>
            <div class="qr-label">Scan for bet verification</div>
          </div>
          `}

          <div class="footer">
            <p><strong>Thank you for your bet. Good luck!</strong></p>
            <p>This is an automatically generated bet slip.</p>
            <p>Printed on: ${new Date().toLocaleString()}</p>
            ${printers.length > 0 ? `<p>Printer: ${printers[0].name}</p>` : ''}
          </div>
        </body>
      </html>
    `;

    // Open print window with enhanced content
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();

      // Wait for content to load before printing
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    } else {
      // Fallback: create temporary element and print
      const printElement = document.createElement('div');
      printElement.innerHTML = printContent;
      printElement.style.display = 'none';
      document.body.appendChild(printElement);
      window.print();
      document.body.removeChild(printElement);
    }
  }, [betDetailsData, detectPrinters, notification.transactionId]);

  if (!isVisible) return null;

  return (
    <div
      className={`fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 ${className}`}
      onClick={handleClose}
    >
      {/* Popup Container */}
      <div
        className="relative bg-filter rounded-lg p-5 flex flex-col items-center gap-3"
        style={{
          width: '450px',
          height: '380px',
          backgroundColor: '#1D1D1F',
          borderRadius: '8px',
          padding: '20px',
          gap: '12px'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Icon */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-white hover:text-gray-300 transition-colors"
          aria-label="Close popup"
        >
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>

        {/* Success Tick Icon */}
        <div className="flex-shrink-0 w-auto h-[170px]">
          <SVGLoader
            name="tick"
            className="w-auto h-auto"
            aria-label="Success"
            fallback={<div className="w-[130px] h-[148px] bg-green-100 rounded-full flex items-center justify-center text-green-600 text-4xl">✓</div>}
          />
        </div>

        {/* Title */}
        <h2 className="font-rubik font-bolder text-center text-2xl leading-none capitalize text-golden">
          Bet Placed Successfully
        </h2>

        {/* Description */}
        <p className="font-rubik font-normal text-center text-base leading-none text-gray-300 w-[80%] leading-[22px]">
          You can view your active bets anytime in the “My Bets” section.
        </p>

        {/* Print Betslip Button */}
        <div className="mt-auto w-full">
          <PrimaryButton
            onClick={handlePrintBetslip}
            size="lg"
            className='py-[1rem]'
            fullWidth={true}
            disabled={isLoadingBetDetails || isPrintingDetectionLoading}
          >
            {isLoadingBetDetails ? 'Loading...' : isPrintingDetectionLoading ? 'Detecting Printers...' : 'Print Betslip'}
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
};

// Simplified toast component for backward compatibility
interface BetNotificationToastProps {
  notification: CashierTurboPlaceBetDetails;
  onClose?: () => void;
  className?: string;
}

export const BetNotificationToast: React.FC<BetNotificationToastProps> = ({
  notification,
  onClose,
  className = ''
}) => {
  // Use the main BetNotification component for consistency
  return (
    <BetNotification
      notification={notification}
      onClose={onClose}
      className={className}
    />
  );
};
